const { BrowserWindow, screen } = require('electron')
const { join } = require('path')

const isDev = process.env.NODE_ENV === 'development'

/**
 * 悬浮窗管理类
 */
class FloatingWindowManager {
  constructor(appManager) {
    this.appManager = appManager
    this.window = null
    this._isFloatingWindowDragging = false // 悬浮窗拖拽状态
    this._dragStartBounds = null // 拖拽开始时的窗口尺寸
    this._originalMinSize = null // 原始最小尺寸
    this._originalMaxSize = null // 原始最大尺寸
  }

  /**
   * 创建悬浮窗
   */
  createWindow() {
    // 如果悬浮窗已存在，则直接显示
    if (this.window) {
      this.window.show()
      return this.window
    }

    console.log('Creating floating window...')

    // 获取屏幕信息
    const { width, height } = screen.getPrimaryDisplay().workAreaSize
    console.log(`屏幕工作区尺寸: ${width}x${height}`)

    // 悬浮窗尺寸和位置计算
    const reverseScale = 1.0 / this.appManager.displayScale
    console.log(`悬浮窗缩放系数: ${reverseScale}`)

    // 悬浮窗初始尺寸 - 折叠状态（不应用缩放，因为Electron会自动处理）
    const floatingWidth = 400  // 逻辑像素宽度
    const floatingHeight = 290 // 逻辑像素高度（折叠状态）

    // 位置计算（使用逻辑像素）- 恢复原始右下角位置
    const margin = 20
    let floatingX = width - floatingWidth - margin
    let floatingY = height - floatingHeight - margin // 原始设计：右下角位置

    // 边界检查 - 确保窗口完全在屏幕内
    const minX = 0
    const maxX = width - floatingWidth
    const minY = 0
    const maxY = height - floatingHeight

    floatingX = Math.max(minX, Math.min(floatingX, maxX))
    floatingY = Math.max(minY, Math.min(floatingY, maxY))

    console.log(`悬浮窗初始位置: x=${floatingX}, y=${floatingY} (屏幕: ${width}x${height}, 窗口: ${floatingWidth}x${floatingHeight})`)

    // 创建浮动窗口
    this.window = new BrowserWindow({
      width: floatingWidth,
      height: floatingHeight, // 初始高度为折叠高度（290px）
      x: floatingX,
      y: floatingY,
      frame: false,
      alwaysOnTop: true,
      skipTaskbar: true,
      resizable: true, // 允许调整大小以支持展开/折叠
      minimizable: false,
      maximizable: false,
      closable: false,
      focusable: true,
      show: false,
      transparent: true,
      hasShadow: true,
      titleBarStyle: 'hidden',
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: isDev
          ? join(__dirname, '../../preload/preload.js')
          : join(__dirname, '../renderer/dist-electron/preload.js'),
        webSecurity: !isDev,
        allowRunningInsecureContent: isDev,
        experimentalFeatures: true,
        enableSharedArrayBuffer: true,
        crossOriginIsolated: false
      }
    })

    // 设置窗口尺寸限制 - 支持动态高度调整
    this.window.setMinimumSize(400, 290) // 最小高度290（折叠状态）
    this.window.setMaximumSize(400, 800) // 最大高度800（完全展开状态）

    // 设置窗口标题
    this.window.setTitle('犇犇助手')

    // 加载悬浮窗页面
    const floatingUrl = isDev
      ? 'http://localhost:6913/floating.html'
      : `file://${join(__dirname, '../../../dist/floating.html')}`

    console.log('Loading floating window URL:', floatingUrl)
    this.window.loadURL(floatingUrl)

    // 开发环境下自动打开开发者工具
    if (isDev) {
      this.window.webContents.openDevTools()
      console.log('开发环境：已打开悬浮窗开发者工具')
    }

    // 窗口准备显示时显示窗口
    this.window.once('ready-to-show', () => {
      console.log('Floating window ready to show')
      this.window.show()
      
      // 设置全局变量以便其他模块访问
      global.floatingWindow = this.window
    })

    // 窗口关闭时的处理
    this.window.on('closed', () => {
      console.log('Floating window closed')
      this.window = null
      global.floatingWindow = null
    })

    // 🔄 【新增】监听窗口大小变化，实现智能定位
    this.window.on('resize', () => {
      this.handleWindowResize()
    })

    // 悬浮窗加载完成后的处理
    this.window.webContents.once('did-finish-load', () => {
      console.log('📋 为悬浮窗注入腾讯云配置...')

      // 应用窗口反向缩放
      console.log('悬浮窗加载完成，应用反向缩放以抵消Windows缩放:', this.appManager.displayScale)
      this.appManager.applyWindowScale(this.window)

      // 向悬浮窗发送应用初始化完成消息
      console.log('向悬浮窗发送应用初始化完成消息')
      this.window.webContents.send('app-initialized', {
        isLoggedIn: this.appManager.isLoggedIn,
        servicesReady: this.appManager.servicesInitialized
      })

      // 🔄 【新增】发送腾讯云配置到悬浮窗
      const tencentConfig = {
        secretId: process.env.TENCENT_SECRET_ID || '',
        secretKey: process.env.TENCENT_SECRET_KEY || '',
        region: process.env.TENCENT_REGION || 'ap-beijing',
        appId: process.env.TENCENT_APP_ID || '',
        // 语音识别配置
        asrEngineType: '16k_zh',
        asrVoiceFormat: 1,
        // TTS配置
        ttsVoiceType: 1002, // 女声
        ttsSpeed: 0, // 正常语速
        ttsVolume: 5, // 音量
        ttsSampleRate: 16000,
        ttsCodec: 'mp3'
      }

      console.log('📋 发送腾讯云配置到悬浮窗:', {
        hasSecretId: !!tencentConfig.secretId,
        hasSecretKey: !!tencentConfig.secretKey,
        region: tencentConfig.region
      })

      this.window.webContents.send('tencent-config', tencentConfig)
    })

    return this.window
  }

  /**
   * 处理窗口大小变化
   */
  handleWindowResize() {
    if (!this.window || this.window.isDestroyed()) return

    try {
      const currentBounds = this.window.getBounds()
      const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize
      
      // 获取新的高度
      const newHeight = currentBounds.height
      
      // 🔄 【修复】检查是否为预期的高度调整（展开/折叠操作）
      const expectedHeights = [120, 200, 300, 400, 500, 600] // 预期的高度值
      const tolerance = 10 // 允许的误差范围
      const isExpectedHeight = expectedHeights.some(h => Math.abs(newHeight - h) <= tolerance)
      
      // 🔄 【修复】计算高度差异
      const heightDiff = Math.abs(newHeight - currentBounds.height)
      
      // 🔄 【修复】拖拽状态检查
      if (this._isFloatingWindowDragging) {
        // 拖拽过程中，只允许预期的高度调整
        if (!isExpectedHeight) {
          console.log('⚠️ 拖拽过程中检测到非预期的窗口大小调整，忽略:', {
            currentHeight: currentBounds.height,
            newHeight: newHeight,
            isExpectedHeight: isExpectedHeight,
            timestamp: new Date().toISOString()
          })
          return
        }
      } else {
        // 非拖拽过程中，允许正常的高度调整
        console.log('✅ 非拖拽过程中，允许窗口高度调整:', {
          currentHeight: currentBounds.height,
          newHeight: newHeight,
          heightDiff: heightDiff,
          isExpectedHeight: isExpectedHeight
        })
      }
      
      // 🔄 【修复】智能定位：保持窗口底部位置不变
      if (heightDiff > 5) { // 只有当高度变化超过5像素时才调整位置
        // 计算新的Y位置，保持底部位置不变
        let newY = currentBounds.y + (currentBounds.height - newHeight)
        
        // 边界检查：确保窗口不超出屏幕
        const margin = 20
        const minY = 0
        const maxY = screenHeight - newHeight - margin

        // 限制Y坐标范围
        newY = Math.max(minY, Math.min(newY, maxY))

        // 同时检查X坐标，防止窗口在屏幕外
        let newX = currentBounds.x
        const maxX = screenWidth - currentBounds.width - margin
        const minX = 0 // 不允许窗口超出屏幕左边缘

        newX = Math.max(minX, Math.min(newX, maxX))
        
        console.log('🔄 调整悬浮窗位置以保持底部对齐:', {
          oldPosition: { x: currentBounds.x, y: currentBounds.y },
          newPosition: { x: newX, y: newY },
          heightChange: newHeight - currentBounds.height
        })
        
        // 设置新位置
        this.window.setPosition(newX, newY)
      }
    } catch (error) {
      console.error('处理悬浮窗大小变化失败:', error)
    }
  }

  /**
   * 重新定位悬浮窗
   */
  reposition() {
    if (!this.window || this.window.isDestroyed()) return

    try {
      const { width, height } = screen.getPrimaryDisplay().workAreaSize
      const currentBounds = this.window.getBounds()

      // 使用当前窗口的实际尺寸
      const floatingWidth = currentBounds.width
      const floatingHeight = currentBounds.height
      const margin = 20

      // 计算新位置（与初始位置计算逻辑一致）- 右下角位置
      let floatingX = width - floatingWidth - margin
      let floatingY = height - floatingHeight - margin // 原始设计：右下角位置

      // 边界检查 - 确保窗口完全在屏幕内
      const minX = 0
      const maxX = width - floatingWidth
      const minY = 0
      const maxY = height - floatingHeight

      floatingX = Math.max(minX, Math.min(floatingX, maxX))
      floatingY = Math.max(minY, Math.min(floatingY, maxY))

      console.log(`重新定位悬浮窗到: x=${floatingX}, y=${floatingY} (屏幕: ${width}x${height}, 窗口: ${floatingWidth}x${floatingHeight})`)
      this.window.setPosition(floatingX, floatingY)
    } catch (error) {
      console.error('重新定位悬浮窗失败:', error)
    }
  }

  /**
   * 设置拖拽状态
   */
  setDragging(isDragging) {
    this._isFloatingWindowDragging = isDragging
    
    if (isDragging) {
      // 记录拖拽开始时的窗口尺寸
      if (this.window && !this.window.isDestroyed()) {
        this._dragStartBounds = this.window.getBounds()
        console.log('🖱️ 悬浮窗拖拽开始，禁用大小调整，初始尺寸:', this._dragStartBounds)
        
        // 立即锁定窗口尺寸，防止任何调整
        this.window.setResizable(false)
        
        // 设置窗口最小和最大尺寸为当前尺寸，防止任何变化
        const currentBounds = this.window.getBounds()
        this.window.setMinimumSize(currentBounds.width, currentBounds.height)
        this.window.setMaximumSize(currentBounds.width, currentBounds.height)
        
        // 记录原始尺寸限制，用于拖拽结束后恢复
        this._originalMinSize = [400, 290]
        this._originalMaxSize = [400, 800]
      }
    } else {
      // 拖拽结束
      console.log('🖱️ 悬浮窗拖拽结束，恢复大小调整功能')
      
      // 延迟清理，确保没有其他操作修改窗口尺寸
      setTimeout(() => {
        this._dragStartBounds = null
        
        // 恢复窗口可调整大小
        if (this.window && !this.window.isDestroyed()) {
          this.window.setResizable(true)
          // 恢复原始窗口尺寸限制，允许动态调整高度
          if (this._originalMinSize) {
            this.window.setMinimumSize(this._originalMinSize[0], this._originalMinSize[1]) // 恢复原始最小尺寸
          } else {
            this.window.setMinimumSize(400, 290)
          }
          
          if (this._originalMaxSize) {
            this.window.setMaximumSize(this._originalMaxSize[0], this._originalMaxSize[1])
          } else {
            this.window.setMaximumSize(400, 800)
          }
        }
        
        // 清理记录的原始尺寸
        this._originalMinSize = null
        this._originalMaxSize = null
      }, 100)
    }
  }

  /**
   * 显示窗口
   */
  show() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.show()
    } else {
      this.createWindow()
    }
  }

  /**
   * 隐藏窗口
   */
  hide() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.hide()
    }
  }

  /**
   * 获取窗口实例
   */
  getWindow() {
    return this.window
  }

  /**
   * 检查窗口是否存在且未销毁
   */
  isValid() {
    return this.window && !this.window.isDestroyed()
  }

  /**
   * 销毁窗口
   */
  destroy() {
    if (this.window && !this.window.isDestroyed()) {
      this.window.destroy()
      this.window = null
      global.floatingWindow = null
    }
  }
}

module.exports = FloatingWindowManager
