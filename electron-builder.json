{"appId": "com.nezha.desktop", "productName": "犇犇数字员工助手", "protocols": [{"name": "ai-cognidesk", "schemes": ["ai-cognidesk"]}], "directories": {"output": "dist"}, "files": ["src/main/**/*", "src/renderer/dist-electron/**/*", "src/renderer/scripts/**/*", "dist/**/*", "package.json", "start-browser-mcp.js", "test-browser-mcp.js"], "asarUnpack": ["node_modules/@playwright/**/*", "node_modules/@modelcontextprotocol/**/*", "src/renderer/scripts/**/*"], "includeSubNodeModules": true, "buildDependenciesFromSource": false, "npmRebuild": false, "extraResources": [{"from": "public/assets", "to": "assets", "filter": ["**/*"]}, {"from": "public/utils", "to": "utils", "filter": ["**/*"]}, {"from": "python", "to": "python", "filter": ["**/*"]}, {"from": "mcp-servers", "to": "mcp-servers", "filter": ["**/*"]}, {"from": "external_tools", "to": "external_tools", "filter": ["**/*"]}, {"from": "mcp-config.json", "to": "mcp-config.json"}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "public/assets/logo.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"artifactName": "${productName} 内测V1.0.4 Setup.${ext}", "oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "犇犇数字员工助手", "include": "installer.nsi"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "public/assets/logo.ico", "category": "public.app-category.productivity"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}], "icon": "public/assets/logo.ico", "category": "Utility"}, "publish": {"provider": "github", "owner": "nezha-team", "repo": "nezha-desktop"}}