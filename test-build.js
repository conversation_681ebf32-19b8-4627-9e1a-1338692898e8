const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 开始测试构建修复...');

// 1. 检查 package.json 版本
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
console.log('📦 Package.json 版本:', packageJson.version);

// 2. 检查 electron-builder 配置
const builderConfig = JSON.parse(fs.readFileSync('electron-builder.json', 'utf8'));
console.log('🔧 Files 配置:', builderConfig.files);

// 3. 检查 preload 文件是否存在
const preloadPath = 'src/preload/preload.js';
if (fs.existsSync(preloadPath)) {
  console.log('✅ Preload 文件存在:', preloadPath);
} else {
  console.log('❌ Preload 文件不存在:', preloadPath);
}

// 4. 检查构建后的 preload 文件
const builtPreloadPath = 'src/renderer/dist-electron/preload.js';
if (fs.existsSync(builtPreloadPath)) {
  console.log('✅ 构建后的 Preload 文件存在:', builtPreloadPath);
} else {
  console.log('❌ 构建后的 Preload 文件不存在:', builtPreloadPath);
}

// 5. 检查主窗口配置
const mainWindowPath = 'src/main/window/mainWindow.js';
const mainWindowContent = fs.readFileSync(mainWindowPath, 'utf8');
if (mainWindowContent.includes('isDev') && mainWindowContent.includes('preload')) {
  console.log('✅ 主窗口 preload 路径配置已更新');
} else {
  console.log('❌ 主窗口 preload 路径配置可能有问题');
}

// 6. 检查悬浮窗配置
const floatingWindowPath = 'src/main/window/floatingWindow.js';
const floatingWindowContent = fs.readFileSync(floatingWindowPath, 'utf8');
if (floatingWindowContent.includes('isDev') && floatingWindowContent.includes('preload')) {
  console.log('✅ 悬浮窗 preload 路径配置已更新');
} else {
  console.log('❌ 悬浮窗 preload 路径配置可能有问题');
}

// 7. 检查版本工具配置
const versionUtilsPath = 'src/renderer/utils/versionUtils.js';
const versionUtilsContent = fs.readFileSync(versionUtilsPath, 'utf8');
if (versionUtilsContent.includes('1.0.4-beta')) {
  console.log('✅ 版本工具 fallback 版本已更新');
} else {
  console.log('❌ 版本工具 fallback 版本可能有问题');
}

console.log('🎯 测试完成！');
